/* Import Fonts */
@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100..900;1,100..900&family=Squada+One&display=swap');

/* Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Roboto', sans-serif;
}

/* body {
  line-height: 1.6;
  background-color: #fff;
} */

/* Hero Section */
.hero {
  background-color: #1AC073; /* Green background */
  width: 100%;   /* full width */
  height: 80px;    /* navbar height */
  display: flex;
  align-items: center;
}

/* Navbar Layout */
.navbar {
  max-width: 1440px;
  width: 100%;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 146px;  /* left/right padding from figma */
}

/* Logo */
.logo {
  font-family: 'Squada One', cursive;
  font-size: 32px;
  font-weight: 400;
  color: #fff;
  line-height: 20px;
  letter-spacing: -0.02em;
}

/* Nav Links */
.nav-links {
  list-style: none;
  display: flex;
  gap: 40px; /* spacing between menu items */
}

.nav-links a {
  text-decoration: none;
  font-family: 'Roboto', sans-serif;
  font-size: 20px;
  color: #fff;
}

/* Icons */
.nav-icons {
  display: flex;
  gap: 20px; /* spacing between icons */
}

.icon {
  font-size: 22px;
  color: #fff;
  cursor: pointer;
}

/* Hero Section */
.hero-content {
  background-color: #1AC073;
  width: 100%;
  height: 720px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.hero-content-inner {
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 132px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 108px;
  width: 100%;
}

/* Left Side (Text + Search) */
.left-hero {
  max-width: 534px;
}

.left-hero h1 {
  font-size: 70px;
  font-weight: 400;
  line-height: 1.17;
  color: #FFFFFF;
  margin-bottom: 32px;
}

.left-hero p {
  font-size: 18px;
  color: #FFFFFF;
  margin-bottom: 32px;
  max-width: 441px;
  line-height: 1.5;
}

/* Search Box */
.hero-search {
  display: flex;
  background: #FFFFFF;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.hero-search input {
  flex: 1;
  padding: 19px 22px;
  border: none;
  outline: none;
  font-size: 15px;
  color: #929292;
}

.hero-search input::placeholder {
  color: #929292;
}

.hero-search button {
  background-color: #F3BA00;
  color: #FFFFFF;
  border: none;
  padding: 19px 29px;
  font-size: 16px;
  font-weight: 400;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.hero-search button:hover {
  background-color: #e6a800;
}

/* Right Side (Illustration) */
.right-hero img {
  width: 521.63px;
  height: 525px;
  object-fit: contain;
}



/* Kitchen Section Styles */
.kitchen-section {
  background-color: #F7F8FA;
  padding: 105px 0;
  width: 100%;
}

.kitchen-container {
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 132px;
}

.kitchen-title {
  font-size: 40px;
  font-weight: 800;
  color: #000000;
  margin-bottom: 60px;
  line-height: 1.2;
}

/* Grid layout */
.food-grid {
  display: grid;
  grid-template-columns: repeat(4, 277px); /* Each card fixed width */
  gap: 24px;
  justify-content: center;
  margin-bottom: 40px;
}

/* Food Card */
.food-card {
  width: 277px;
  height: 337px;
  background: #FFFFFF;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 0 1px rgba(187, 187, 187, 0.25);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
}

.food-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}

/* Food Image */
.food-image {
  position: relative;
  width: 277px;
  height: 250px;
  overflow: hidden;
}

.food-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.discount-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: #F3BA00;
  color: #101115;
  padding: 5px 8px;
  border-radius: 10px 0 0 0;
  font-size: 12px;
  font-weight: 500;
}

/* Food Details */
.food-details {
  flex: 1;
  padding: 10px 16px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.food-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.food-name {
  font-size: 16px;
  font-weight: 500;
  color: #1B1C21;
}

.price-tag {
  background-color: #BEBEBE;
  color: #1B1C21;
  padding: 2px 6px;
  border-radius: 5px;
  font-size: 14px;
  font-weight: 500;
}

/* Meta Info */
.food-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rating,
.delivery-time {
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: #F7F8FA;
  padding: 2px 6px;
  border-radius: 5px;
  font-size: 12px;
  color: #1B1C21;
  line-height: 1.4;
}

.star-icon {
  color: #F3BA00;
  font-size: 14px;
}

/* Add to Cart Button */
.add-to-cart {
  width: 28px;
  height: 28px;
  background-color: #F3BA00;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.add-to-cart svg {
  width: 12px;
  height: 12px;
}

.add-to-cart:hover {
  background-color: #e6a800;
}








/* Service Section */
.service-section {
  background-color: #FAFAFA;
  padding: 80px 0;
  position: relative;
  min-height: 860px;
  max-width: 1440px;
  margin: 0 auto;
}

.service-background {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 344px;
  background-color: #C7EACC;
  width: 100%;
}

.service-container {
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 132px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 50px;
  position: relative;
  z-index: 1;
}

.service-content {
  text-align: center;
  max-width: 864px;
}

.service-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 40px;
  font-weight: 700;
  line-height: 1.425;
  color: #252B42;
  margin-bottom: 10px;
  letter-spacing: 0.5px;
}

.service-description {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.6;
  color: #737373;
  letter-spacing: 0.5px;
}

.service-card {
  position: relative;
  width: 877px;
  height: 442px;
  border: 1px solid #DEDEDE;
  border-radius: 5px;
  overflow: hidden;
}

.service-image {
  position: relative;
  width: 100%;
  height: 100%;
}

.service-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.service-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 15%, rgba(56, 56, 56, 0.84) 100%);
}

.service-arrow {
  position: absolute;
  top: 50%;
  right: 50%;
  transform: translate(50%, -50%);
}

.arrow-circle {
  width: 97.5px;
  height: 97.5px;
  background-color: #1AC073;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 4px 5px 20px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: background 0.3s, transform 0.3s;
}

.arrow-circle i {
  font-size: 28px;
  color: #fff;
}

.arrow-circle:hover {
  background-color: #17a561;
  transform: scale(1.1);
}










/* Contact Section Styles */
.contact-section {
    background-color: #F7F8FA;
    padding: 100px 0;
    width: 100%;
}

.contact-container {
    max-width: 1440px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 103px;
    padding: 0 132px;
}

.contact-content {
    max-width: 575px;
}

.contact-title {
    font-size: 24px;
    font-weight: 400;
    color: #101115;
    margin-bottom: 5px;
    line-height: 1;
}

.contact-description {
    font-size: 12px;
    color: #1B1C21;
    margin-bottom: 105px;
    line-height: 1.67;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.form-row {
    display: flex;
    gap: 16px;
}

.form-input {
    flex: 1;
    padding: 19px 22px;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    color: #929292;
    box-shadow: 0 0 1px rgba(0, 0, 0, 0.25);
}

.form-input::placeholder {
    color: #929292;
}

.form-textarea {
    padding: 19px 22px;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    color: #929292;
    box-shadow: 0 0 1px rgba(0, 0, 0, 0.25);
    resize: vertical;
    min-height: 164px;
    font-family: 'Roboto', sans-serif;
}

.form-textarea::placeholder {
    color: #929292;
}

.submit-btn {
    background-color: #1AC073;
    color: #FFFFFF;
    border: none;
    padding: 19px 25px;
    font-size: 16px;
    font-weight: 400;
    border-radius: 10px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    width: 210px;
    align-self: flex-start;
}

.submit-btn:hover {
    background-color: #15a065;
}

.contact-illustration {
    flex-shrink: 0;
}

.contact-img {
    width: 503px;
    height: 402px;
    object-fit: cover;
}





/* Footer Styles */
.footer {
  background-color: #1AC073;
  padding: 40px 0;
  color: #fff;
  width: 100%;
}

.footer-container {
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 132px;
  width: 100%;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 30px;
}

.footer-logo {
  font-family: 'Squada One', cursive;
  font-size: 32px;
  font-weight: 400;
  color: #fff;
  line-height: 20px;
  letter-spacing: -0.02em;
}

.footer-menu {
  display: flex;
  list-style: none;
  gap: 40px;
}

.footer-menu a {
  color: #fff;
  text-decoration: none;
  font-size: 20px;
  font-weight: 400;
  font-family: 'Roboto', sans-serif;
  transition: opacity 0.3s;
}

.footer-menu a:hover {
  opacity: 0.8;
}

.social-icons {
  display: flex;
  gap: 20px;
}

.social-icon {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s, transform 0.3s;
  color: #fff;
  font-size: 18px;
  text-decoration: none;
}

.social-icon:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.footer-divider {
  height: 1px;
  background-color: rgba(255, 255, 255, 0.3);
  width: 100%;
}
